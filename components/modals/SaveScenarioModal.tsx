import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

interface SaveScenarioModalProps {
  isOpen: boolean;
  onClose: () => void;
  inputData: any;
  currentScenarioId: number | null;
  householdId: number;
  annotations?: Array<{
    x: string;
    y: string;
    text: string;
    color: string;
    series: string;
  }>;
}

export default function SaveScenarioModal({
  isOpen,
  onClose,
  inputData,
  currentScenarioId,
  householdId,
  annotations = []
}: SaveScenarioModalProps) {
  const [saveOption, setSaveOption] = useState<'overwrite' | 'new'>('overwrite');
  const [newScenarioName, setNewScenarioName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState<{ type: 'success' | 'error', message: string } | null>(null);
  const router = useRouter();
  const [newScenarioId, setNewScenarioId] = useState<number | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [scenarioDetails, setScenarioDetails] = useState<any>(null);

  // Fetch scenario details when the modal opens
  useEffect(() => {
    if (isOpen && currentScenarioId) {
      fetchScenarioDetails(currentScenarioId);
    }

    if (isOpen) {
      setSaveOption('overwrite');
      setNotification(null);
      fetchCurrentUser();
    }
  }, [isOpen, currentScenarioId]);

  // Fetch scenario details from the database
  const fetchScenarioDetails = async (scenarioId: number) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('scenarios_data1')
        .select('scenario_name')
        .eq('id', scenarioId)
        .single();

      if (error) {
        console.error('Error fetching scenario details:', error);
        return;
      }

      if (data) {
        console.log('Fetched scenario details:', data);
        setScenarioDetails(data);
        // Set the new scenario name to the fetched scenario name
        setNewScenarioName(data.scenario_name || '');
      }
    } catch (error) {
      console.error('Error in fetchScenarioDetails:', error);
    }
  };

  const fetchCurrentUser = async () => {
    const supabase = createClient();
    const { data } = await supabase.auth.getUser();
    if (data.user) {
      setUserId(data.user.id);
    }
  };

  const handleSave = async () => {
    console.log('Save button clicked, saveOption:', saveOption);

    if (!householdId) {
      setNotification({ type: 'error', message: 'Invalid household ID. Please try again.' });
      return;
    }

    if (!userId) {
      setNotification({ type: 'error', message: 'User authentication error. Please try again.' });
      return;
    }

    // Validate scenario name
    if (saveOption === 'new' && !newScenarioName.trim()) {
      setNotification({ type: 'error', message: 'Please enter a scenario name.' });
      return;
    }

    // For overwrite option, ensure we have a valid scenario ID and name
    if (saveOption === 'overwrite') {
      if (!currentScenarioId) {
        setNotification({ type: 'error', message: 'No scenario selected to overwrite.' });
        return;
      }

      if (!scenarioDetails?.scenario_name) {
        // If we don't have the scenario details yet, wait a moment and try again
        setTimeout(handleSave, 500);
        return;
      }
    }

    setIsLoading(true);
    const supabase = createClient();
    const now = new Date().toISOString();

    // Prepare the data to be saved - matching the new schema

    const dataToSave = {
      // Required fields
      scenario_name: saveOption === 'new' ? newScenarioName : (scenarioDetails?.scenario_name || inputData.scenario_name || `Scenario ${Date.now()}`),
      household_id: householdId,
      household_name: inputData.household_name,
      user_id: userId, // Add user_id to the data being saved

      // Debug info
      id: saveOption === 'overwrite' ? currentScenarioId : undefined, // Include ID for debugging

      // Annotations
      annotations: annotations,

      // What-If scenarios
      what_if: inputData.whatIfEvents || [],

      // Personal details
      name: inputData.name || null,
      starting_age: inputData.starting_age || null,
      ending_age: inputData.ending_age || null,
      include_partner: !!inputData.partner_name,
      partner_name: inputData.partner_name || null,
      partner_starting_age: inputData.partner_starting_age || null,

      // Income details
      annual_income: inputData.annual_income || null,
      income_period: inputData.income_period || [null, null],
      partner_annual_income: inputData.partner_annual_income || null,
      partner_income_period: inputData.partner_income_period || [null, null],
      include_superannuation: inputData.superannuation || false,
      additional_incomes: Array.isArray(inputData.additional_incomes) ? inputData.additional_incomes : [],

      // Savings and expenses
      savings_amount: inputData.savings_amount || null,
      cash_reserve: inputData.cash_reserve || null,
      saving_percentage: inputData.saving_percentage || null,
      annual_expenses1: inputData.annual_expenses1 || null,
      expense_period1: inputData.expense_period1 || [null, null],
      annual_expenses2: inputData.annual_expenses2 || null,
      expense_period2: inputData.expense_period2 || [null, null],
      additional_expenses: Array.isArray(inputData.additional_expenses) ? inputData.additional_expenses : [],
      second_expense: inputData.second_expense,

      // Legacy Investment details (for backward compatibility)
      initial_investment: inputData.initial_investment || null,
      annual_investment_contribution: inputData.annual_investment_contribution || null,
      annual_investment_return: inputData.annual_investment_return || null,
      inv_std_dev: inputData.inv_std_dev || null,
      fund_type: inputData.inv_fund_type || null,
      fund_periods: Array.isArray(inputData.fund_periods) ? inputData.fund_periods : [],
      investment_return_period: inputData.investment_return_period || [null, null],
      contribution_period: inputData.contribution_period || [null, null],
      inv_tax: inputData.inv_tax || null,
      investment_description: inputData.investment_description || null,
      one_off_investments: Array.isArray(inputData.one_off_investments) ? inputData.one_off_investments : [],

      // Store all investment metrics in a JSONB column
      investment_metrics: (() => {
        // Create a comprehensive metrics object
        const metrics = {
          // Legacy investment fund data
          legacy: {
            initial_investment: inputData.initial_investment || 0,
            annual_investment_contribution: inputData.annual_investment_contribution || 0,
            annual_investment_return: inputData.annual_investment_return || 0,
            inv_std_dev: inputData.inv_std_dev || 0,
            fund_type: inputData.inv_fund_type || null,
            investment_return_period: inputData.investment_return_period || [null, null],
            contribution_period: inputData.contribution_period || [null, null],
            inv_tax: inputData.inv_tax || null,
            investment_description: inputData.investment_description || null,
          },

          // Individual funds data
          funds: {} as { [key: string]: any },

          // Withdrawal priorities
          withdrawal_priorities: Array.isArray(inputData.withdrawal_priorities) ? inputData.withdrawal_priorities : [],

          // One-off investments
          one_off_investments: Array.isArray(inputData.one_off_investments) ? inputData.one_off_investments : []
        };

        // Get active funds from withdrawal priorities
        const activeFunds = Array.isArray(inputData.withdrawal_priorities) ? inputData.withdrawal_priorities : [];

        // Add metrics for each fund in the withdrawal priorities list
        for (const fundNumber of activeFunds) {
          metrics.funds[`fund${fundNumber}`] = {
            fund_index: fundNumber,
            initial_investment: (inputData as any)[`initial_investment${fundNumber}`] || 0,
            annual_investment_contribution: (inputData as any)[`annual_investment_contribution${fundNumber}`] || 0,
            annual_investment_return: (inputData as any)[`annual_investment_return${fundNumber}`] || 0,
            inv_std_dev: (inputData as any)[`inv_std_dev${fundNumber}`] || 0,
            investment_description: (inputData as any)[`investment_description${fundNumber}`] || `Investment Fund ${fundNumber}`,
            fund_periods: Array.isArray((inputData as any)[`fund_periods${fundNumber}`])
              ? (inputData as any)[`fund_periods${fundNumber}`]
              : [],
            investment_return_period: (inputData as any)[`investment_return_period${fundNumber}`] || inputData.investment_return_period || [null, null],
            contribution_period: (inputData as any)[`contribution_period${fundNumber}`] || inputData.contribution_period || [null, null],
            inv_tax: (inputData as any)[`inv_tax${fundNumber}`] || inputData.inv_tax || null,
          };
        }

        // Also add any funds that have data but aren't in the withdrawal priorities
        for (let i = 1; i <= 5; i++) {
          if (!activeFunds.includes(i) && (inputData as any)[`initial_investment${i}`] !== undefined) {
            metrics.funds[`fund${i}`] = {
              fund_index: i,
              initial_investment: (inputData as any)[`initial_investment${i}`] || 0,
              annual_investment_contribution: (inputData as any)[`annual_investment_contribution${i}`] || 0,
              annual_investment_return: (inputData as any)[`annual_investment_return${i}`] || 0,
              inv_std_dev: (inputData as any)[`inv_std_dev${i}`] || 0,
              investment_description: (inputData as any)[`investment_description${i}`] || `Investment Fund ${i}`,
              fund_periods: Array.isArray((inputData as any)[`fund_periods${i}`])
                ? (inputData as any)[`fund_periods${i}`]
                : [],
              investment_return_period: (inputData as any)[`investment_return_period${i}`] || inputData.investment_return_period || [null, null],
              contribution_period: (inputData as any)[`contribution_period${i}`] || inputData.contribution_period || [null, null],
              inv_tax: (inputData as any)[`inv_tax${i}`] || inputData.inv_tax || null,
            };
          }
        }

        return metrics;
      })(),

      // We're now storing withdrawal_priorities in the investment_metrics JSONB field

      // KiwiSaver details
      initial_kiwisaver: inputData.initial_kiwiSaver || null,
      kiwisaver_contribution: inputData.kiwisaver_contribution,
      employer_contribution: inputData.employer_contribution,
      annual_kiwisaver_return: inputData.annual_kiwisaver_return,
      ks_std_dev: inputData.ks_std_dev,
      ks_periods: inputData.ks_periods || [],
      consolidate_kiwisaver: !!inputData.consolidate_kiwisaver,
      consolidate_kiwisaver_age: inputData.consolidate_kiwisaver_age,
      // Save KiwiSaver consolidation allocations
      consolidation_allocations: inputData.consolidation_allocations || [],

      // Partner KiwiSaver
      partner_initial_kiwisaver: inputData.partner_initial_kiwisaver || null,
      partner_kiwisaver_contribution: inputData.partner_kiwisaver_contribution || null,
      partner_employer_contribution: inputData.partner_employer_contribution || null,
      partner_annual_kiwisaver_return: inputData.partner_annual_kiwisaver_return || null,
      partner_ks_std_dev: inputData.partner_ks_std_dev || null,
      partner_ks_periods: inputData.partner_ks_periods || [],
      partner_consolidate_kiwisaver: !!inputData.partner_consolidate_kiwisaver,
      partner_consolidate_kiwisaver_age: inputData.partner_consolidate_kiwisaver_age || null,
      // Save partner KiwiSaver consolidation allocations
      partner_consolidation_allocations: inputData.partner_consolidation_allocations || [],

      // Property details - keeping original fields for backward compatibility
      property_value: inputData.property_value || null,
      debt: inputData.debt || null,
      property_growth: inputData.property_growth || null,
      debt_ir: inputData.debt_ir || null,
      initial_debt_years: inputData.initial_debt_years || null,
      additional_debt_repayments: inputData.additional_debt_repayments || null,
      include_property_debt: inputData.include_property_debt || false,
      show_repayments: inputData.show_repayments || false,
      sell_main_property: inputData.sell_main_property || false,
      main_property_sale_age: inputData.main_property_sale_age || null,
      main_prop_sale_value: inputData.main_prop_sale_value || null,
      sale_allocate_to_investment: inputData.sale_allocate_to_investment || false,
      downsize: inputData.downsize || false,
      downsize_age: inputData.downsize_age || null,
      new_property_value: inputData.new_property_value || null,
      allocate_to_investment: inputData.allocate_to_investment || false,
      pay_off_debt: inputData.pay_off_debt || false,

      // Interest-only periods for property 1 - keeping for backward compatibility
      interest_only_period: inputData.interest_only_period || false,
      interest_only_start_age: inputData.interest_only_start_age || null,
      interest_only_end_age: inputData.interest_only_end_age || null,

      // Rental/Board income fields for property 1 - keeping for backward compatibility
      rental_income: inputData.rental_income || false,
      rental_amount: inputData.rental_amount || null,
      rental_start_age: inputData.rental_start_age || null,
      rental_end_age: inputData.rental_end_age || null,
      board_income: inputData.board_income || false,
      board_amount: inputData.board_amount || null,
      board_start_age: inputData.board_start_age || null,
      board_end_age: inputData.board_end_age || null,

      // All properties data in JSONB format
      properties_data: [
        // Property 1
        {
          property_index: 1,
          property_value: inputData.property_value || null,
          property_growth: inputData.property_growth || null,
          debt: inputData.debt || null,
          debt_ir: inputData.debt_ir || null,
          initial_debt_years: inputData.initial_debt_years || null,
          additional_debt_repayments: inputData.additional_debt_repayments || null,
          additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age || null,
          additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age || null,
          sell_main_property: inputData.sell_main_property || false,
          main_property_sale_age: inputData.main_property_sale_age || null,
          main_prop_sale_value: inputData.main_prop_sale_value || null,
          downsize: inputData.downsize || false,
          downsize_age: inputData.downsize_age || null,
          new_property_value: inputData.new_property_value || null,
          allocate_to_investment: inputData.allocate_to_investment || false,
          pay_off_debt: inputData.pay_off_debt || false,
          sale_allocate_to_investment: inputData.sale_allocate_to_investment || false,
          interest_only_period: inputData.interest_only_period || false,
          interest_only_start_age: inputData.interest_only_start_age || null,
          interest_only_end_age: inputData.interest_only_end_age || null,
          rental_income: inputData.rental_income || false,
          rental_amount: inputData.rental_amount || null,
          rental_start_age: inputData.rental_start_age || null,
          rental_end_age: inputData.rental_end_age || null,
          board_income: inputData.board_income || false,
          board_amount: inputData.board_amount || null,
          board_start_age: inputData.board_start_age || null,
          board_end_age: inputData.board_end_age || null,
          lump_sum_payment_age: inputData.lump_sum_payment_age || null,
          lump_sum_payment_amount: inputData.lump_sum_payment_amount || null,
          lump_sum_payment_source: inputData.lump_sum_payment_source || null,
          property_title: inputData.property_title,
          // Purchase details
          show_purchase_details: inputData.show_purchase_details || false,
          purchase_age: inputData.purchase_age || null,
          deposit_amount: inputData.deposit_amount || null,
          deposit_sources: inputData.deposit_sources || null
        },
        // Property 2
        {
          property_index: 2,
          property_value: inputData.property_value2 || null,
          property_growth: inputData.property_growth2 || null,
          debt: inputData.debt2 || null,
          debt_ir: inputData.debt_ir2 || null,
          initial_debt_years: inputData.initial_debt_years2 || inputData.debt_years2 || null,
          additional_debt_repayments: inputData.additional_debt_repayments2 || null,
          additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age2 || null,
          additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age2 || null,
          sell_main_property: inputData.sell_main_property2 || false,
          main_property_sale_age: inputData.main_property_sale_age2 || null,
          main_prop_sale_value: inputData.main_prop_sale_value2 || null,
          pay_off_debt: inputData.pay_off_debt2 || false,
          property_title: inputData.property_title2 || null,
          downsize: inputData.downsize2 || false,
          downsize_age: inputData.downsize_age2 || null,
          new_property_value: inputData.new_property_value2 || null,
          allocate_to_investment: inputData.allocate_to_investment2 || false,

          // Interest-only periods
          interest_only_period: inputData.interest_only_period2 || false,
          interest_only_start_age: inputData.interest_only_start_age2 || null,
          interest_only_end_age: inputData.interest_only_end_age2 || null,

          // Rental income
          rental_income: inputData.rental_income2 || false,
          rental_amount: inputData.rental_amount2 || null,
          rental_start_age: inputData.rental_start_age2 || null,
          rental_end_age: inputData.rental_end_age2 || null,

          // Board income
          board_income: inputData.board_income2 || false,
          board_amount: inputData.board_amount2 || null,
          board_start_age: inputData.board_start_age2 || null,
          board_end_age: inputData.board_end_age2 || null,

          // Lump sum payments
          lump_sum_payment_age: inputData.lump_sum_payment_age2 || null,
          lump_sum_payment_amount: inputData.lump_sum_payment_amount2 || null,
          lump_sum_payment_source: inputData.lump_sum_payment_source2 || null,

          // Purchase details
          show_purchase_details: inputData.show_purchase_details2 || false,
          purchase_age: inputData.purchase_age2 || null,
          deposit_amount: inputData.deposit_amount2 || null,
          deposit_sources: inputData.deposit_sources2 || null,

          sale_allocate_to_investment: inputData.sale2_allocate_to_investment || false,
        },
        // Property 3
        {
          property_index: 3,
          property_value: inputData.property_value3 || null,
          property_growth: inputData.property_growth3 || null,
          debt: inputData.debt3 || null,
          debt_ir: inputData.debt_ir3 || null,
          initial_debt_years: inputData.initial_debt_years3 || inputData.debt_years3 || null,
          additional_debt_repayments: inputData.additional_debt_repayments3 || null,
          additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age3 || null,
          additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age3 || null,
          starting_age: inputData.starting_age3 || null,
          downsize: inputData.downsize3 || false,
          downsize_age: inputData.downsize_age3 || null,
          new_property_value: inputData.new_property_value3 || null,
          allocate_to_investment: inputData.allocate_to_investment3 || false,
          sell_main_property: inputData.sell_main_property3 || false,
          main_property_sale_age: inputData.main_property_sale_age3 || null,
          main_prop_sale_value: inputData.main_prop_sale_value3 || null,
          pay_off_debt: inputData.pay_off_debt3 || false,
          sale_allocate_to_investment: inputData.sale3_allocate_to_investment || false,
          interest_only_period: inputData.interest_only_period3 || false,
          interest_only_start_age: inputData.interest_only_start_age3 || null,
          interest_only_end_age: inputData.interest_only_end_age3 || null,
          rental_income: inputData.rental_income3 || false,
          rental_amount: inputData.rental_amount3 || null,
          rental_start_age: inputData.rental_start_age3 || null,
          rental_end_age: inputData.rental_end_age3 || null,
          board_income: inputData.board_income3 || false,
          board_amount: inputData.board_amount3 || null,
          board_start_age: inputData.board_start_age3 || null,
          board_end_age: inputData.board_end_age3 || null,
          lump_sum_payment_age: inputData.lump_sum_payment_age3 || null,
          lump_sum_payment_amount: inputData.lump_sum_payment_amount3 || null,
          lump_sum_payment_source: inputData.lump_sum_payment_source3 || null,
          property_title: inputData.property_title3 || null,
          // Purchase details
          show_purchase_details: inputData.show_purchase_details3 || false,
          purchase_age: inputData.purchase_age3 || null,
          deposit_amount: inputData.deposit_amount3 || null,
          deposit_sources: inputData.deposit_sources3 || null
        },
        // Property 4
        {
          property_index: 4,
          property_value: inputData.property_value4 || null,
          property_growth: inputData.property_growth4 || null,
          debt: inputData.debt4 || null,
          debt_ir: inputData.debt_ir4 || null,
          initial_debt_years: inputData.initial_debt_years4 || inputData.debt_years4 || null,
          additional_debt_repayments: inputData.additional_debt_repayments4 || null,
          additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age4 || null,
          additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age4 || null,
          starting_age: inputData.starting_age4 || null,
          downsize: inputData.downsize4 || false,
          downsize_age: inputData.downsize_age4 || null,
          new_property_value: inputData.new_property_value4 || null,
          allocate_to_investment: inputData.allocate_to_investment4 || false,
          sell_main_property: inputData.sell_main_property4 || false,
          main_property_sale_age: inputData.main_property_sale_age4 || null,
          main_prop_sale_value: inputData.main_prop_sale_value4 || null,
          pay_off_debt: inputData.pay_off_debt4 || false,
          sale_allocate_to_investment: inputData.sale4_allocate_to_investment || false,
          interest_only_period: inputData.interest_only_period4 || false,
          interest_only_start_age: inputData.interest_only_start_age4 || null,
          interest_only_end_age: inputData.interest_only_end_age4 || null,
          rental_income: inputData.rental_income4 || false,
          rental_amount: inputData.rental_amount4 || null,
          rental_start_age: inputData.rental_start_age4 || null,
          rental_end_age: inputData.rental_end_age4 || null,
          board_income: inputData.board_income4 || false,
          board_amount: inputData.board_amount4 || null,
          board_start_age: inputData.board_start_age4 || null,
          board_end_age: inputData.board_end_age4 || null,
          lump_sum_payment_age: inputData.lump_sum_payment_age4 || null,
          lump_sum_payment_amount: inputData.lump_sum_payment_amount4 || null,
          lump_sum_payment_source: inputData.lump_sum_payment_source4 || null,
          property_title: inputData.property_title4 || null,
          // Purchase details
          show_purchase_details: inputData.show_purchase_details4 || false,
          purchase_age: inputData.purchase_age4 || null,
          deposit_amount: inputData.deposit_amount4 || null,
          deposit_sources: inputData.deposit_sources4 || null
        },
        // Property 5
        {
          property_index: 5,
          property_value: inputData.property_value5 || null,
          property_growth: inputData.property_growth5 || null,
          debt: inputData.debt5 || null,
          debt_ir: inputData.debt_ir5 || null,
          initial_debt_years: inputData.initial_debt_years5 || inputData.debt_years5 || null,
          additional_debt_repayments: inputData.additional_debt_repayments5 || null,
          additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age5 || null,
          additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age5 || null,
          starting_age: inputData.starting_age5 || null,
          downsize: inputData.downsize5 || false,
          downsize_age: inputData.downsize_age5 || null,
          new_property_value: inputData.new_property_value5 || null,
          allocate_to_investment: inputData.allocate_to_investment5 || false,
          sell_main_property: inputData.sell_main_property5 || false,
          main_property_sale_age: inputData.main_property_sale_age5 || null,
          main_prop_sale_value: inputData.main_prop_sale_value5 || null,
          pay_off_debt: inputData.pay_off_debt5 || false,
          sale_allocate_to_investment: inputData.sale5_allocate_to_investment || false,
          interest_only_period: inputData.interest_only_period5 || false,
          interest_only_start_age: inputData.interest_only_start_age5 || null,
          interest_only_end_age: inputData.interest_only_end_age5 || null,
          rental_income: inputData.rental_income5 || false,
          rental_amount: inputData.rental_amount5 || null,
          rental_start_age: inputData.rental_start_age5 || null,
          rental_end_age: inputData.rental_end_age5 || null,
          board_income: inputData.board_income5 || false,
          board_amount: inputData.board_amount5 || null,
          board_start_age: inputData.board_start_age5 || null,
          board_end_age: inputData.board_end_age5 || null,
          lump_sum_payment_age: inputData.lump_sum_payment_age5 || null,
          lump_sum_payment_amount: inputData.lump_sum_payment_amount5 || null,
          lump_sum_payment_source: inputData.lump_sum_payment_source5 || null,
          property_title: inputData.property_title5 || null,
          // Purchase details
          show_purchase_details: inputData.show_purchase_details5 || false,
          purchase_age: inputData.purchase_age5 || null,
          deposit_amount: inputData.deposit_amount5 || null,
          deposit_sources: inputData.deposit_sources5 || null
        }
      ].filter(prop => prop.property_value !== null),

      additional_properties: Array.isArray(inputData.additional_properties) ? inputData.additional_properties : [],

      // Other settings
      inflation_rate: inputData.inflation_rate || null,
      main_income_inflation_rate: inputData.main_income_inflation_rate || null,
      partner_income_inflation_rate: inputData.partner_income_inflation_rate || null,
      expense1_inflation_rate: inputData.expense1_inflation_rate || null,
      expense2_inflation_rate: inputData.expense2_inflation_rate || null,
      simulations: inputData.num_simulations || null,
      confidence_interval: inputData.confidence_interval || null,
      seed: inputData.seed || null,
      sale_investment_fund: inputData.sale_investment_fund || '',
      fund_diversion: inputData.fund_diversion,
      utilise_excess_cashflow: inputData.utilise_excess_cashflow,

      created_at: now,

      // Legacy investment_funds_data field (kept for backward compatibility)
      // Now we use investment_metrics for all fund data
      investment_funds_data: null,

      // Timestamps
      last_edited_at: now,
      last_viewed_at: now,
    };


    try {

      if (saveOption === 'overwrite' && currentScenarioId) {

        const { error } = await supabase
          .from('scenarios_data1')  // Updated table name
          .update(dataToSave)
          .eq('id', currentScenarioId);

        if (error) {
          console.error('Error updating scenario:', error);
          throw error;
        }

        setNewScenarioId(currentScenarioId);
        setNotification({ type: 'success', message: 'Scenario updated successfully!' });

        // Verification step complete


      } else {
        const { data, error } = await supabase
          .from('scenarios_data1')  // Updated table name
          .insert({ ...dataToSave, created_at: now })
          .select()
          .single();

        if (error) throw error;
        setNewScenarioId(data.id);
        setNotification({ type: 'success', message: 'New scenario created successfully!' });

        // Verification step complete

      }
    } catch (error) {
      console.error('Error saving scenario:', error);
      setNotification({ type: 'error', message: 'Error saving scenario. Please try again.' });
    } finally {
      setIsLoading(false);
    }

  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="overflow-y-auto max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Save Scenario</DialogTitle>
        </DialogHeader>
        {notification?.type !== 'success' ? (
          <>
            <div className="flex space-x-4 mb-4">
              <Button
                onClick={() => {
                  setSaveOption('overwrite');
                }}
                variant={saveOption === 'overwrite' ? 'default' : 'outline'}
                type="button"
              >
                Overwrite existing
              </Button>
              <Button
                onClick={() => setSaveOption('new')}
                variant={saveOption === 'new' ? 'default' : 'outline'}
              >
                Save as new
              </Button>
            </div>
            {saveOption === 'new' && (
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="scenarioName">Scenario Name</Label>
                <Input
                  type="text"
                  id="scenarioName"
                  placeholder="Enter scenario name"
                  value={newScenarioName}
                  onChange={(e) => setNewScenarioName(e.target.value)}
                />
              </div>
            )}
            <DialogFooter>
              {saveOption === 'new' ? (
                <Button
                  onClick={() => {
                    handleSave();
                  }}
                  disabled={!newScenarioName || isLoading}
                  type="button"
                >
                  {isLoading ? 'Saving...' : 'Save as New'}
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    handleSave();
                  }}
                  disabled={isLoading}
                  type="button"
                >
                  {isLoading ? 'Saving...' : 'Overwrite Existing'}
                </Button>
              )}
            </DialogFooter>
          </>
        ) : (
          <>
            <div className="mt-4 flex items-center justify-end gap-4">
              <span className="text-gray-500">Go To</span>
              <Button
                variant="outline"
                onClick={() => {
                  onClose();
                  router.push(`/protected/planner?scenarioId=${newScenarioId}`);
                }}
              >
                New Scenario
              </Button>
              <Button variant="outline" onClick={onClose}>
                Existing Scenario
              </Button>
            </div>
            <div className={`mt-4 p-2 rounded ${notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {notification.message}
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
