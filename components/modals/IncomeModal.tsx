'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Textarea } from "@/components/ui/textarea";
import { Income } from '@/components/tables/Income_ExpenseTable';

// Using the Income interface imported from Income_ExpenseTable

interface IncomeModalProps {
  isOpen: boolean;
  onClose: () => void;
  householdId: string;
  onSuccess: () => void;
  incomeToEdit?: Income;
}

interface HouseholdMember {
  id: string;
  name: string;
  is_main_member: boolean;
}

export default function IncomeModal({ isOpen, onClose, householdId, onSuccess, incomeToEdit }: IncomeModalProps) {
  const [source, setSource] = useState('');
  const [amount, setAmount] = useState('');
  const [frequency, setFrequency] = useState('monthly');
  const [details, setDetails] = useState('');
  const [incomeType, setIncomeType] = useState('gross');
  const [memberId, setMemberId] = useState('');
  const [members, setMembers] = useState<HouseholdMember[]>([]);
  const [loading, setLoading] = useState(false);

  // Load income data when editing
  useEffect(() => {
    if (isOpen && incomeToEdit) {
      setSource(incomeToEdit.source);
      setAmount(incomeToEdit.amount.toString());
      setFrequency(incomeToEdit.frequency);
      setIncomeType(incomeToEdit.income_type || 'gross');
      setDetails(incomeToEdit.details || '');

      // Set member ID if available
      if (incomeToEdit.member_id !== undefined && incomeToEdit.member_id !== null) {
        setMemberId(incomeToEdit.member_id.toString());
      } else {
        // Default to household (0) if no member_id
        setMemberId('0');
      }
    }
  }, [isOpen, incomeToEdit]);

  useEffect(() => {
    const fetchMembers = async () => {
      if (!householdId) {
        console.error('No household ID provided');
        return;
      }

      const supabase = createClient();

      // First, get the specific household by ID
      const { data: householdData, error: householdError } = await supabase
        .from('households')
        .select('*')
        .eq('id', householdId)
        .single();

      if (householdError) {
        console.error('Error fetching household:', householdError);
        return;
      }

      if (!householdData) {
        console.error('No household found with ID:', householdId);
        return;
      }

      console.log('Household data:', householdData);

      // Process members from the household data
      let formattedMembers: HouseholdMember[] = [];

      // Always add a Household option (member_id = 0)
      formattedMembers.push({
        id: '0',
        name: 'Household',
        is_main_member: false
      });

      // Check if the household has members data
      if (householdData.members) {
        const membersData = householdData.members as any;
        console.log('Members data structure:', membersData);

        // Handle the specific structure with name1, name2, etc.
        // Extract all nameX properties and create member objects
        const nameKeys = Object.keys(membersData).filter(key => key.startsWith('name'));

        const memberObjects = nameKeys.map(nameKey => {
          // Extract the index number (e.g., '1' from 'name1')
          const memberIndex = nameKey.replace('name', '');
          const name = membersData[nameKey];

          // Only add if name exists
          if (name) {
            return {
              id: memberIndex,
              name: name,
              // Consider the first member (name1) as the main member
              is_main_member: memberIndex === '1'
            };
          }
          return null;
        }).filter(Boolean) as HouseholdMember[];

        // Add individual members after the household option
        formattedMembers = [...formattedMembers, ...memberObjects];
      }

      console.log('Formatted members:', formattedMembers);

      // Set members state
      setMembers(formattedMembers);

      // Set default member if applicable
      if (formattedMembers.length === 1) {
        setMemberId(formattedMembers[0].id);
      } else {
        // If there's a main member, set them as default
        const mainMember = formattedMembers.find(m => m.is_main_member);
        if (mainMember) {
          setMemberId(mainMember.id);
        }
      }
    };

    if (isOpen) {
      fetchMembers();
    }
  }, [isOpen, householdId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const incomeData = {
      household_id: householdId,
      source,
      amount: parseFloat(amount),
      frequency,
      income_type: incomeType,
      details,
      member_id: memberId ? parseInt(memberId) : 0 // Use 0 for household instead of null
    };

    console.log('Saving income with member_id:', memberId);

    const supabase = createClient();
    let error;

    if (incomeToEdit?.id) {
      // Update existing income
      const { error: updateError } = await supabase
        .from('income')
        .update(incomeData)
        .eq('id', incomeToEdit.id);

      error = updateError;
    } else {
      // Insert new income
      const { error: insertError } = await supabase
        .from('income')
        .insert([incomeData]);

      error = insertError;
    }

    setLoading(false);

    if (error) {
      console.error(`Error ${incomeToEdit ? 'updating' : 'adding'} income:`, error);
    } else {
      onSuccess();
      handleClose();
    }
  };

  const handleClose = () => {
    setSource('');
    setAmount('');
    setFrequency('monthly');
    setDetails('');
    setIncomeType('gross');
    setMemberId('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{incomeToEdit ? 'Edit Income' : 'Add Income'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="member">Member</Label>
            <Select
              value={memberId}
              onValueChange={setMemberId}
              disabled={members.length === 1}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select member" />
              </SelectTrigger>
              <SelectContent>
                {members.map((member) => (
                  <SelectItem key={member.id} value={member.id}>
                    {member.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="source">Source</Label>
            <Input
              id="source"
              value={source}
              onChange={(e) => setSource(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="frequency">Frequency</Label>
            <Select value={frequency} onValueChange={setFrequency}>
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="fortnightly">Fortnightly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="annually">Annually</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="incomeType">Income Type</Label>
            <Select value={incomeType} onValueChange={setIncomeType}>
              <SelectTrigger>
                <SelectValue placeholder="Select income type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gross">Gross</SelectItem>
                <SelectItem value="net">Net</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="details">Details</Label>
            <Textarea
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (incomeToEdit ? 'Updating...' : 'Adding...') : (incomeToEdit ? 'Update Income' : 'Add Income')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
