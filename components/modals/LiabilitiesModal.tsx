'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

interface LiabilitiesModalProps {
  isOpen: boolean;
  onClose: () => void;
  householdId: string;
  onSuccess: () => void;
  liability?: {
    linked_asset_id: string;
    id: string;
    name: string;
    type: string;
    amount: number;
    interest_rate?: number;
    lender?: string;
    payment_amount?: number;
    payment_frequency?: string;
    loan_type?: string;
    details?: string;
    linked_expense_id?: string;
  };
}

export default function LiabilitiesModal({ isOpen, onClose, householdId, onSuccess, liability }: LiabilitiesModalProps) {
  const [name, setName] = useState(liability?.name || '');
  const [type, setType] = useState(liability?.type || 'mortgage');
  const [amount, setAmount] = useState(liability?.amount?.toString() || '');
  const [interestRate, setInterestRate] = useState(liability?.interest_rate?.toString() || '');
  const [lender, setLender] = useState(liability?.lender || '');
  const [paymentAmount, setPaymentAmount] = useState(liability?.payment_amount?.toString() || '');
  const [paymentFrequency, setPaymentFrequency] = useState(liability?.payment_frequency || 'monthly');
  const [loanType, setLoanType] = useState(liability?.loan_type || 'principal_and_interest');
  const [linkedAssetId, setLinkedAssetId] = useState(liability?.linked_asset_id || 'none');
  const [details, setDetails] = useState(liability?.details || '');
  const [loading, setLoading] = useState(false);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [showLoanTypeField, setShowLoanTypeField] = useState(true);
  const [createLinkedExpense, setCreateLinkedExpense] = useState(false);
  const [expenseAmount, setExpenseAmount] = useState('');
  const [expenseFrequency, setExpenseFrequency] = useState('monthly');
  const [expenseCategory, setExpenseCategory] = useState('debt');

  useEffect(() => {
    const fetchAssets = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('assets')
        .select('id, name, type')
        .eq('household_id', householdId);

      if (error) {
        console.error('Error fetching assets:', error);
      } else if (data) {
        setAssets(data);
      }
    };

    if (isOpen) {
      fetchAssets();
    }
  }, [isOpen, householdId]);

  useEffect(() => {
    setShowLoanTypeField(['mortgage', 'car_loan', 'personal_loan', 'business_loan'].includes(type));
    if (!showLoanTypeField) {
      setLoanType('');
    }
  }, [type]);

  useEffect(() => {
    if (liability && isOpen) {
      setName(liability.name);
      setType(liability.type);
      setAmount(liability.amount.toString());
      setInterestRate(liability.interest_rate?.toString() || '');
      setLender(liability.lender || '');
      setPaymentAmount(liability.payment_amount?.toString() || '');
      setPaymentFrequency(liability.payment_frequency || 'monthly');
      setLoanType(liability.loan_type || 'principal_and_interest');
      setLinkedAssetId(liability.linked_asset_id || 'none');
      setDetails(liability.details || '');
    }
  }, [liability, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const supabase = createClient();
    let liabilityId = liability?.id;
    let linkedExpenseId = null;

    try {
      // Create or update the liability
      const liabilityData = {
        household_id: householdId,
        name,
        type,
        amount: parseFloat(amount),
        interest_rate: interestRate ? parseFloat(interestRate) : null,
        lender: lender || null,
        payment_amount: paymentAmount ? parseFloat(paymentAmount) : null,
        payment_frequency: paymentFrequency || null,
        loan_type: showLoanTypeField ? loanType : null,
        linked_asset_id: linkedAssetId === 'none' ? null : linkedAssetId || null,
        details: details || null
      };

      if (liabilityId) {
        // Update existing liability
        const { error: updateError } = await supabase
          .from('liabilities')
          .update(liabilityData)
          .eq('id', liabilityId);
        
        if (updateError) throw updateError;
      } else {
        // Create new liability
        const { data: newLiability, error: insertError } = await supabase
          .from('liabilities')
          .insert([liabilityData])
          .select();
        
        if (insertError) throw insertError;
        if (newLiability && newLiability.length > 0) {
          liabilityId = newLiability[0].id;
        }
      }

      // Handle linked expense if needed
      if (createLinkedExpense && liabilityId && paymentAmount) {
        const expenseData = {
          household_id: householdId,
          name: `${name} Payment`,
          amount: parseFloat(expenseAmount || paymentAmount),
          frequency: expenseFrequency || paymentFrequency || 'monthly',
          category: expenseCategory,
          details: `Payment for ${name}`,
          linked_liability_id: liabilityId
        };

        // Create or update linked expense
        if (liability?.linked_expense_id) {
          const { error: updateExpenseError } = await supabase
            .from('expenses')
            .update(expenseData)
            .eq('id', liability.linked_expense_id);
          
          if (updateExpenseError) throw updateExpenseError;
          linkedExpenseId = liability.linked_expense_id;
        } else {
          const { data: newExpense, error: insertExpenseError } = await supabase
            .from('expenses')
            .insert([expenseData])
            .select();
          
          if (insertExpenseError) throw insertExpenseError;
          if (newExpense && newExpense.length > 0) {
            linkedExpenseId = newExpense[0].id;
          }
        }

        // Update liability with linked expense id
        if (linkedExpenseId) {
          const { error: linkError } = await supabase
            .from('liabilities')
            .update({ linked_expense_id: linkedExpenseId })
            .eq('id', liabilityId);
          
          if (linkError) throw linkError;
        }
      }

      setLoading(false);
      onSuccess();
      handleClose();
    } catch (error) {
      setLoading(false);
      console.error('Error saving liability:', error);
    }
  };

  const handleClose = () => {
    setName('');
    setType('mortgage');
    setAmount('');
    setInterestRate('');
    setLender('');
    setPaymentAmount('');
    setPaymentFrequency('monthly');
    setLoanType('principal_and_interest');
    setLinkedAssetId('none');
    setDetails('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{liability ? 'Edit Liability' : 'Add Liability'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mortgage">Mortgage</SelectItem>
                <SelectItem value="car_loan">Car Loan</SelectItem>
                <SelectItem value="personal_loan">Personal Loan</SelectItem>
                <SelectItem value="credit_card">Credit Card</SelectItem>
                <SelectItem value="student_loan">Student Loan</SelectItem>
                <SelectItem value="business_loan">Business Loan</SelectItem>
                <SelectItem value="tax_debt">Tax Debt</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="interestRate">Interest Rate (%)</Label>
            <Input
              id="interestRate"
              type="number"
              step="0.01"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="lender">Lender</Label>
            <Input
              id="lender"
              value={lender}
              onChange={(e) => setLender(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="paymentAmount">Payment Amount</Label>
            <Input
              id="paymentAmount"
              type="number"
              step="0.01"
              value={paymentAmount}
              onChange={(e) => setPaymentAmount(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="paymentFrequency">Payment Frequency</Label>
            <Select value={paymentFrequency} onValueChange={setPaymentFrequency}>
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="fortnightly">Fortnightly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="annually">Annually</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {showLoanTypeField && (
            <div className="space-y-2">
              <Label htmlFor="loanType">Loan Type</Label>
              <Select value={loanType} onValueChange={setLoanType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select loan type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="principal_and_interest">Principal and Interest</SelectItem>
                  <SelectItem value="interest_only">Interest Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="linkedAsset">Linked Asset</Label>
            <Select value={linkedAssetId} onValueChange={setLinkedAssetId}>
              <SelectTrigger>
                <SelectValue placeholder="Select linked asset" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {assets.map((asset) => (
                  <SelectItem key={asset.id} value={asset.id}>
                    {asset.name} ({asset.type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="details">Details</Label>
            <Textarea
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
            />
          </div>

          {paymentAmount && paymentFrequency && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="createLinkedExpense" 
                  checked={createLinkedExpense}
                  onCheckedChange={(checked) => {
                    setCreateLinkedExpense(checked === true);
                    if (checked && paymentAmount) {
                      setExpenseAmount(paymentAmount);
                      setExpenseFrequency(paymentFrequency);
                    }
                  }}
                />
                <Label htmlFor="createLinkedExpense">Add as expense</Label>
              </div>
              
              {createLinkedExpense && (
                <div className="space-y-4 pl-6 pt-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expenseAmount">Expense Amount</Label>
                      <Input
                        id="expenseAmount"
                        type="number"
                        value={expenseAmount || paymentAmount}
                        onChange={(e) => setExpenseAmount(e.target.value)}
                        placeholder="0.00"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="expenseFrequency">Frequency</Label>
                      <Select value={expenseFrequency || paymentFrequency} onValueChange={setExpenseFrequency}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="fortnightly">Fortnightly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                          <SelectItem value="annually">Annually</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="expenseCategory">Category</Label>
                    <Select value={expenseCategory} onValueChange={setExpenseCategory}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="debt">Debt</SelectItem>
                        <SelectItem value="housing">Housing</SelectItem>
                        <SelectItem value="utilities">Utilities</SelectItem>
                        <SelectItem value="transportation">Transportation</SelectItem>
                        <SelectItem value="insurance">Insurance</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (liability ? 'Updating...' : 'Adding...') : (liability ? 'Update Liability' : 'Add Liability')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

interface Asset {
  id: string;
  name: string;
  type: string;
}
