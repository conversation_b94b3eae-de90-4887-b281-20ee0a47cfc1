'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/hooks/use-toast";

interface AssetsModalProps {
  isOpen: boolean;
  onClose: () => void;
  householdId: string;
  onSuccess: () => void;
  asset?: {
    id: string;
    name: string;
    type: string;
    value: number;
    details?: string;
    linked_income_id?: string;
    fund_type?: string;
    member_id?: string;
    provider?: string;
    property_type?: string;
    rental_income?: number;
  };
}

export default function AssetsModal({ isOpen, onClose, householdId, onSuccess, asset }: AssetsModalProps) {
  const [name, setName] = useState(asset?.name || '');
  const [type, setType] = useState(asset?.type || 'property');
  const [value, setValue] = useState(asset?.value?.toString() || '');
  const [details, setDetails] = useState(asset?.details || '');
  const [propertyType, setPropertyType] = useState<string | undefined>(asset?.type === 'property' ? 'owner_occupied' : undefined);
  const [rentalIncome, setRentalIncome] = useState('');
  const [provider, setProvider] = useState('');
  const [fundType, setFundType] = useState(asset?.fund_type || '');
  const [memberId, setMemberId] = useState(asset?.member_id || '');
  const [loading, setLoading] = useState(false);
  const [showPropertyFields, setShowPropertyFields] = useState(asset?.type === 'property');
  const [showProviderField, setShowProviderField] = useState(['investment', 'savings', 'superannuation', 'kiwisaver'].includes(asset?.type || ''));
  const [showFundTypeField, setShowFundTypeField] = useState(asset?.type === 'kiwisaver');
  const [showMemberField, setShowMemberField] = useState(asset?.type === 'kiwisaver');
  const [createLinkedIncome, setCreateLinkedIncome] = useState(false);
  const [incomeAmount, setIncomeAmount] = useState('');
  const [incomeFrequency, setIncomeFrequency] = useState('monthly');
  const [incomeType, setIncomeType] = useState('gross');
  const [householdMembers, setHouseholdMembers] = useState<{[key: string]: string}>({});

  // Load asset data when editing
  useEffect(() => {
    if (asset && isOpen) {
      setName(asset.name);
      setType(asset.type);
      setValue(asset.value.toString());
      setDetails(asset.details || '');
      setProvider(asset.provider || '');
      setFundType(asset.fund_type || '');
      setMemberId(asset.member_id || '');

      if (asset.type === 'property') {
        setPropertyType(asset.property_type || 'owner_occupied');
        if (asset.property_type === 'investment' && asset.rental_income) {
          setRentalIncome(asset.rental_income.toString());
        }
      }

      setShowPropertyFields(asset.type === 'property');
      setShowProviderField(['investment', 'savings', 'superannuation', 'kiwisaver'].includes(asset.type));
      setShowFundTypeField(asset.type === 'kiwisaver');
      setShowMemberField(asset.type === 'kiwisaver');

      // Log the asset data for debugging
      console.log('Loading asset data:', asset);
    }
  }, [asset, isOpen]);

  // Fetch household members
  useEffect(() => {
    const fetchHouseholdMembers = async () => {
      if (!householdId || !isOpen) return;

      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('members')
        .eq('id', householdId)
        .single();

      if (error) {
        console.error('Error fetching household members:', error);
        return;
      }

      if (data?.members) {
        const members = data.members;
        const memberMap: {[key: string]: string} = {};

        // Map member IDs to names
        // 1 = Main member (name1)
        if (members.name1) {
          memberMap['1'] = members.name1;
        }

        // 2 = Partner (name2)
        if (members.name2) {
          memberMap['2'] = members.name2;
        }

        setHouseholdMembers(memberMap);
      }
    };

    fetchHouseholdMembers();
  }, [householdId, isOpen]);

  // Effect to handle showing/hiding conditional fields based on type
  useEffect(() => {
    setShowPropertyFields(type === 'property');
    setShowProviderField(['investment', 'savings', 'superannuation', 'kiwisaver'].includes(type));
    setShowFundTypeField(type === 'kiwisaver');
    setShowMemberField(type === 'kiwisaver');

    // Reset conditional fields when type changes
    if (type !== 'property') {
      setPropertyType(undefined);
      setRentalIncome('');
    }
    if (!['investment', 'savings', 'superannuation', 'kiwisaver'].includes(type)) {
      setProvider('');
    }
    if (type !== 'kiwisaver') {
      setFundType('');
      setMemberId('');
    }
  }, [type]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const supabase = createClient();
    let assetId = asset?.id;
    let linkedIncomeId = null;

    // Get the authenticated user (not used but kept for consistency)
    await supabase.auth.getUser();

    try {
      // Validate required fields
      if (type === 'kiwisaver' && !memberId) {
        toast({
          title: "Member is required",
          description: "Please select a member for the KiwiSaver account",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // Create or update the asset
      const assetData = {
        household_id: householdId,
        name,
        type,
        value: parseFloat(value),
        details,
        property_type: showPropertyFields ? propertyType : null,
        rental_income: propertyType === 'investment' && rentalIncome ? parseFloat(rentalIncome) : null,
        provider: showProviderField ? provider : null,
        fund_type: showFundTypeField ? fundType : null,
        member_id: showMemberField ? memberId : null
      };

      // Log the data being sent to Supabase
      console.log('Saving asset data:', assetData);

      if (assetId) {
        // Update existing asset
        const { error: updateError } = await supabase
          .from('assets')
          .update(assetData)
          .eq('id', assetId);

        if (updateError) throw updateError;
      } else {
        // Create new asset
        const { data: newAsset, error: insertError } = await supabase
          .from('assets')
          .insert([assetData])
          .select();

        if (insertError) throw insertError;
        if (newAsset && newAsset.length > 0) {
          assetId = newAsset[0].id;
        }
      }

      // Handle linked income if needed
      if (createLinkedIncome && assetId &&
          (type === 'property' && propertyType === 'investment' && rentalIncome)) {

        const incomeData = {
          household_id: householdId,
          source: `${name} (Rental)`,
          amount: parseFloat(incomeAmount || rentalIncome),
          frequency: incomeFrequency,
          income_type: incomeType,
          details: `Rental income from ${name}`,
          linked_asset_id: assetId
        };

        // Create or update linked income
        if (asset?.linked_income_id) {
          const { error: updateIncomeError } = await supabase
            .from('income')
            .update(incomeData)
            .eq('id', asset.linked_income_id);

          if (updateIncomeError) throw updateIncomeError;
          linkedIncomeId = asset.linked_income_id;
        } else {
          const { data: newIncome, error: insertIncomeError } = await supabase
            .from('income')
            .insert([incomeData])
            .select();

          if (insertIncomeError) throw insertIncomeError;
          if (newIncome && newIncome.length > 0) {
            linkedIncomeId = newIncome[0].id;
          }
        }

        // Update asset with linked income id
        if (linkedIncomeId) {
          const { error: linkError } = await supabase
            .from('assets')
            .update({ linked_income_id: linkedIncomeId })
            .eq('id', assetId);

          if (linkError) throw linkError;
        }
      }

      setLoading(false);
      onSuccess();
      handleClose();
    } catch (error) {
      setLoading(false);
      console.error('Error saving asset:', error);
    }
  };

  const handleClose = () => {
    setName('');
    setType('property');
    setValue('');
    setDetails('');
    setPropertyType('owner_occupied');
    setRentalIncome('');
    setProvider('');
    setFundType('');
    setMemberId('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{asset ? 'Edit Asset' : 'Add Asset'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="property">Property</SelectItem>
                <SelectItem value="vehicle">Vehicle</SelectItem>
                <SelectItem value="savings">Savings</SelectItem>
                <SelectItem value="investment">Investment</SelectItem>
                <SelectItem value="superannuation">Other Superannuation</SelectItem>
                <SelectItem value="kiwisaver">KiwiSaver</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {showPropertyFields && (
            <>
              <div className="space-y-2">
                <Label htmlFor="propertyType">Property Type</Label>
                <Select value={propertyType} onValueChange={setPropertyType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select property type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="investment">Investment</SelectItem>
                    <SelectItem value="owner_occupied">Owner Occupied</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {propertyType === 'investment' && (
                <div className="space-y-2">
                  <Label htmlFor="rentalIncome">Rental Income</Label>
                  <Input
                    id="rentalIncome"
                    type="number"
                    step="0.01"
                    value={rentalIncome}
                    onChange={(e) => setRentalIncome(e.target.value)}
                  />
                </div>
              )}
            </>
          )}

          {showProviderField && (
            <div className="space-y-2">
              <Label htmlFor="provider">Provider</Label>
              <Input
                id="provider"
                value={provider}
                onChange={(e) => setProvider(e.target.value)}
              />
            </div>
          )}

          {showFundTypeField && (
            <div className="space-y-2">
              <Label htmlFor="fundType">Fund Type</Label>
              <Input
                id="fundType"
                value={fundType}
                onChange={(e) => setFundType(e.target.value)}
              />
            </div>
          )}

          {showMemberField && (
            <div className="space-y-2">
              <Label htmlFor="memberId">Member <span className="text-red-500">*</span></Label>
              <Select
                value={memberId}
                onValueChange={setMemberId}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select member" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(householdMembers).map(([id, name]) => (
                    <SelectItem key={id} value={id}>
                      {name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {showPropertyFields && propertyType === 'investment' && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="createLinkedIncome"
                  checked={createLinkedIncome}
                  onCheckedChange={(checked) => {
                    setCreateLinkedIncome(checked === true);
                    if (checked && rentalIncome) {
                      setIncomeAmount(rentalIncome);
                    }
                  }}
                />
                <Label htmlFor="createLinkedIncome">Add as income source</Label>
              </div>

              {createLinkedIncome && (
                <div className="space-y-4 pl-6 pt-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="incomeAmount">Income Amount</Label>
                      <Input
                        id="incomeAmount"
                        type="number"
                        value={incomeAmount || rentalIncome}
                        onChange={(e) => setIncomeAmount(e.target.value)}
                        placeholder="0.00"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="incomeFrequency">Frequency</Label>
                      <Select value={incomeFrequency} onValueChange={setIncomeFrequency}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="fortnightly">Fortnightly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                          <SelectItem value="annually">Annually</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="incomeType">Income Type</Label>
                    <Select value={incomeType} onValueChange={setIncomeType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gross">Gross</SelectItem>
                        <SelectItem value="net">Net</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="value">Value</Label>
            <Input
              id="value"
              type="number"
              step="0.01"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="details">Details</Label>
            <Textarea
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (asset ? 'Updating...' : 'Adding...') : (asset ? 'Update Asset' : 'Add Asset')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
