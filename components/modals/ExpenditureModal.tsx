'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Textarea } from "@/components/ui/textarea";

// Define the Expense interface
export interface Expense {
  id: number;
  name: string;
  amount: number;
  frequency: string;
  category?: string;
  details?: string;
  household_id?: number;
}

interface ExpenditureModalProps {
  isOpen: boolean;
  onClose: () => void;
  householdId: string;
  onSuccess: () => void;
  expenseToEdit?: Expense;
}

export default function ExpenditureModal({ isOpen, onClose, householdId, onSuccess, expenseToEdit }: ExpenditureModalProps) {
  const [name, setName] = useState('');
  const [amount, setAmount] = useState('');
  const [frequency, setFrequency] = useState('monthly');
  const [category, setCategory] = useState('fixed');
  const [details, setDetails] = useState('');
  const [loading, setLoading] = useState(false);

  // Load expense data when editing
  useEffect(() => {
    if (isOpen && expenseToEdit) {
      setName(expenseToEdit.name);
      setAmount(expenseToEdit.amount.toString());
      setFrequency(expenseToEdit.frequency);
      setCategory(expenseToEdit.category || 'fixed');
      setDetails(expenseToEdit.details || '');
    }
  }, [isOpen, expenseToEdit]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const expenseData = {
      household_id: householdId,
      name,
      amount: parseFloat(amount),
      frequency,
      category,
      details
    };

    const supabase = createClient();
    let error;

    if (expenseToEdit?.id) {
      // Update existing expense
      const { error: updateError } = await supabase
        .from('expenses')
        .update(expenseData)
        .eq('id', expenseToEdit.id);
      
      error = updateError;
    } else {
      // Insert new expense
      const { error: insertError } = await supabase
        .from('expenses')
        .insert([expenseData]);
      
      error = insertError;
    }

    setLoading(false);

    if (error) {
      console.error(`Error ${expenseToEdit ? 'updating' : 'adding'} expense:`, error);
    } else {
      onSuccess();
      handleClose();
    }
  };

  const handleClose = () => {
    setName('');
    setAmount('');
    setFrequency('monthly');
    setCategory('fixed');
    setDetails('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{expenseToEdit ? 'Edit Expense' : 'Add Expense'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="frequency">Frequency</Label>
            <Select value={frequency} onValueChange={setFrequency}>
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="fortnightly">Fortnightly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="annually">Annually</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fixed">Fixed</SelectItem>
                <SelectItem value="variable">Variable</SelectItem>
                <SelectItem value="lifestyle">Lifestyle</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="details">Details</Label>
            <Textarea
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (expenseToEdit ? 'Updating...' : 'Adding...') : (expenseToEdit ? 'Update Expense' : 'Add Expense')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
