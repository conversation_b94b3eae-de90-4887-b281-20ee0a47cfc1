# Fixing Chart Flickering in Presentation View

This document outlines the changes made to the `app/protected/presentation/page.tsx` component to eliminate the flickering and multiple rerenders observed when switching between scenarios, particularly when transitioning from overlay mode back to a single scenario view. The solution involves introducing a new state to manage the transition explicitly and controlling the visibility and re-mounting of the chart components during data loading and calculation.

## Target File

All modifications are made within the `app/protected/presentation/page.tsx` file.

## Changes Overview

The core problem was that multiple state updates were occurring sequentially after fetching scenario data, causing the chart component to re-render with intermediate data states. The fix introduces a `isTransitioning` state to keep the chart hidden until all necessary data is loaded and calculations are complete.

The key changes include:

1.  **Adding a `isTransitioning` State:** A new boolean state variable to indicate when the component is actively switching scenarios or modes and data is being processed.
2.  **Refining State Updates and Effect Dependencies:** Restructuring `useEffect` hooks to ensure a more controlled flow of data loading, processing, and calculation.
3.  **Controlling Chart Visibility:** Using the `chartVisible` state and a small timeout to delay rendering the chart until the data is fully ready, combined with incrementing `chartKey` to force re-mounting and animation reset.
4.  **Updating Data Fetching and Processing:** Modifying `fetchScenarioDetails` and `processScenarioData` to ensure the `inputData` is fully prepared before triggering the calculation effect.
5.  **Managing Transition State:** Setting `isTransitioning` to `true` when a scenario switch or overlay mode toggle (off) is initiated, and setting it back to `false` only after the final calculation is done and the chart is ready to be shown.
6.  **Conditional Loading Spinner:** Displaying a loading spinner during both `isLoading` and `isTransitioning` states.
7.  **Conditional Chart/Table Rendering:** Rendering the chart and table components only when `chartVisible` is true.

## Detailed Changes

Apply the following changes to your `app/protected/presentation/page.tsx` file.

### 1. Add `isTransitioning` State

Introduce a new state variable alongside your other state declarations.

```language:app/protected/presentation/page.tsx
```102:105:app/protected/presentation/page.tsx
  const [isPresentationModalOpen, setIsPresentationModalOpen] = useState(false);
  const [availableScenarios, setAvailableScenarios] = useState<Array<{
    id: number;
    scenario_name: string;
    household_id: number;
    household_name: string;
  }>>([]);
  const [activeScenarioId, setActiveScenarioId] = useState<number | null>(null);

  // Add a state to explicitly manage the transition from overlay to single view
  const [isTransitioning, setIsTransitioning] = useState(false);
```

### 2. Modify Initial Load Scenarios Effect

This effect will now primarily fetch the list of available scenarios and set the `activeScenarioId`. The actual loading and processing of the active scenario's data will be handled by a separate effect.

```language:app/protected/presentation/page.tsx
```118:126:app/protected/presentation/page.tsx
  // Initial load scenarios effect - modified to only load scenarios and set active ID
  useEffect(() => {
    const loadScenarios = async () => {
      setIsLoading(true);
      // Keep chart and data hidden initially
      setDataReady(false);
      setChartVisible(false);

      if (!householdId) {
        setIsLoading(false);
        return;
      }
```
```language:app/protected/presentation/page.tsx
```148:150:app/protected/presentation/page.tsx
          // Set the first scenario as active. The next effect will trigger loading details.
          const firstScenarioId = scenariosData[0].id;
          setActiveScenarioId(firstScenarioId);
```

### 3. Update `processScenarioData` Function

This function now includes the logic to initialize the individual investment funds based on the legacy `initial_investment` field if the individual fields are not already populated. This ensures the `inputData` object is consistently structured.

```language:app/protected/presentation/page.tsx
```909:935:app/protected/presentation/page.tsx
    if (inputData.initial_investment > 0 &&
        !(inputData.initial_investment1 || inputData.initial_investment2 ||
          inputData.initial_investment3 || inputData.initial_investment4 ||
          inputData.initial_investment5)) {

      // Distribute the total investment across all 5 funds
      const totalInvestment = inputData.initial_investment;

      inputData.initial_investment1 = totalInvestment * 0.2;
      inputData.annual_investment_return1 = inputData.annual_investment_return || 5.5;
      inputData.inv_std_dev1 = inputData.inv_std_dev || 8.0;
      inputData.investment_description1 = inputData.investment_description || 'Investment Fund 1';

      inputData.initial_investment2 = totalInvestment * 0.2;
      inputData.annual_investment_return2 = 5.5;
      inputData.inv_std_dev2 = 8.0;
      inputData.investment_description2 = 'Investment Fund 2';

      inputData.initial_investment3 = totalInvestment * 0.2;
      inputData.annual_investment_return3 = 5.5;
      inputData.inv_std_dev3 = 8.0;
      inputData.investment_description3 = 'Investment Fund 3';

      inputData.initial_investment4 = totalInvestment * 0.2;
      inputData.annual_investment_return4 = 5.5;
      inputData.inv_std_dev4 = 8.0;
      inputData.investment_description4 = 'Investment Fund 4';

      inputData.initial_investment5 = totalInvestment * 0.2;
      inputData.annual_investment_contribution5 = inputData.annual_investment_contribution || 0; // Added contribution initialization
      inputData.annual_investment_return5 = 5.5;
      inputData.inv_std_dev5 = 8.0;
      inputData.investment_description5 = 'Investment Fund 5';
```
```language:app/protected/presentation/page.tsx
```937:943:app/protected/presentation/page.tsx
      // Clear the legacy initial_investment field
      inputData.initial_investment = 0;

      // Set withdrawal priorities for all 5 funds
      inputData.withdrawal_priorities = [1, 2, 3, 4, 5];
    }

    // If no active funds in priority list, default to fund 1
    if (!inputData.withdrawal_priorities || inputData.withdrawal_priorities.length === 0) {
       inputData.withdrawal_priorities = [1];
    }
```

### 4. Modify `fetchScenarioDetails` Function

This function is now responsible for fetching scenario data (either from cache or the database), processing it using `processScenarioData`, setting `selectedScenario`, and returning the processed `InputData`. It handles loading and error states.

```language:app/protected/presentation/page.tsx
```950:967:app/protected/presentation/page.tsx
  const fetchScenarioDetails = useCallback(async (id: number) => {
    setIsLoading(true); // Keep isLoading true until calculation is done

    // Check if we have saved input data for this scenario
    if (scenarioInputData[id]) {
      const savedData = scenarioInputData[id];
      // Fetch household name for UI display even if using cached data
      await fetchHouseholdName(savedData.household_id);
      // Set annotations if they exist in the saved data
      if (savedData.annotations) {
        setAnnotations({ netWealth: savedData.annotations });
      }
      // Set selectedScenario here
      const scenarioFromAvailable = availableScenarios.find(s => s.id === id);
      if (scenarioFromAvailable) {
         setSelectedScenario(scenarioFromAvailable);
      }

      return savedData;
    }

    // If no saved data, fetch from database with specific column selection
    const supabase = createClient();
    const { data: scenarioData, error } = await supabase
```
```language:app/protected/presentation/page.tsx
```974:981:app/protected/presentation/page.tsx
       setIsLoading(false);
       setIsTransitioning(false);
       setChartVisible(false);
       setDataReady(false);
       return null;
    }


    if (scenarioData) {
```
```language:app/protected/presentation/page.tsx
```988:1003:app/protected/presentation/page.tsx
      // Process the fetched data into InputData format, including initialization
      const processedData = processScenarioData(scenarioData);

      // Set selectedScenario here
      setSelectedScenario(scenarioData);
      setHasPartner(processedData.includePartner);


      // Return the processed data
      return processedData;
    }

    setIsLoading(false); // Set loading false if no scenario data found
    setIsTransitioning(false);
    setChartVisible(false);
    setDataReady(false);
    return null; // Return null if no data found
  }, [fetchHouseholdName, processScenarioData, scenarioInputData, setAnnotations, setSelectedScenario, availableScenarios, setHasPartner, setIsTransitioning, setIsLoading, setChartVisible, setDataReady]); // Add dependencies
```

### 5. Add Effect to Load Data When `activeScenarioId` Changes

This new effect listens for changes in `activeScenarioId` and triggers the data fetching process using `fetchScenarioDetails`.

```language:app/protected/presentation/page.tsx
```1005:1024:app/protected/presentation/page.tsx
  // Effect to trigger data loading and calculation when activeScenarioId changes
  // or when transitioning out of overlay mode.
  useEffect(() => {
    if (activeScenarioId !== null) {
      const loadData = async () => {
        setIsLoading(true); // Start loading state
        setChartVisible(false); // Hide chart
        setDataReady(false); // Data not ready for animations

        const data = await fetchScenarioDetails(activeScenarioId);

        if (data) {
          // Set the inputData state. This will trigger the calculation useEffect.
          setInputData(data);
          // The calculation useEffect will then set isLoading to false and end the transition.
        } else {
          console.error(`Failed to load details for scenario ${activeScenarioId}`);
          setIsLoading(false); // Stop loading if data fetch failed
          setIsTransitioning(false); // End transition if it was active
        }
      };

      loadData();
    }
  }, [activeScenarioId, fetchScenarioDetails, setIsTransitioning, setIsLoading, setChartVisible, setDataReady]); // Dependencies
```

### 6. Modify Effect for Financial Calculation

This effect now explicitly checks for `inputData`, `activeScenarioId`, and `selectedScenario` before performing calculations. It sets `isLoading` and `isTransitioning` to false *after* the calculation and uses a timeout to reveal the chart.

```language:app/protected/presentation/page.tsx
```1026:1057:app/protected/presentation/page.tsx
  // Effect to perform financial calculation and update UI state when inputData changes
  // and activeScenarioId is set.
  useEffect(() => {
    if (inputData && activeScenarioId !== null && selectedScenario) {

      // Perform calculation using the finalized inputData
      const result = calculateFinancialLife(inputData as unknown as FinancialData);
      setAllMetrics(result.allMetrics);
      setChanceOfSuccess(result.chanceOfSuccess);
      setSuccessfulScenarios(result.successfulScenarios);
      setFailedScenarios(result.failedScenarios);
      setMinNetWealthAtAge(result.worstNetWealthAtAge);
      setMaxNetWealthAtAge(result.bestNetWealthAtAge);
      setAverageNetWealthAtAge(result.averageNetWealthAtAge);

      // Calculation is complete
      setIsLoading(false);
      setIsTransitioning(false); // End transition

      // Force remount of chart and show it after calculation
      setChartKey(prevKey => prevKey + 1);
      // Use a single timeout to manage visibility and data readiness
      setTimeout(() => {
        setChartVisible(true);
        setDataReady(true);
      }, 100); // Small delay to allow DOM updates

      // Save the input data to scenario-specific state AFTER calculation is done
      // This ensures we cache the version of inputData that was used for the calculation.
      setScenarioInputData(prev => ({
        ...prev,
        [activeScenarioId]: inputData
      }));
    } else if (inputData && activeScenarioId === null) {
       // Safeguard: should not happen with current logic flow
       console.warn("Input data available but no active scenario ID.");
       setIsLoading(false);
       setIsTransitioning(false);
       setChartVisible(false);
       setDataReady(false);
    }

  }, [inputData, activeScenarioId, selectedScenario, calculateFinancialLife, setIsLoading, setIsTransitioning, setChartKey, setChartVisible, setDataReady, setAllMetrics, setChanceOfSuccess, setSuccessfulScenarios, setFailedScenarios, setMinNetWealthAtAge, setMaxNetWealthAtAge, setAverageNetWealthAtAge, setScenarioInputData]); // Added all relevant state setters as dependencies
```

### 7. Modify `handleScenarioChange` Function

This function is now responsible for starting the transition (`setIsTransitioning(true)`) and setting the `activeScenarioId`. The data loading and calculation are handled by the dedicated effects.

```language:app/protected/presentation/page.tsx
```1059:1075:app/protected/presentation/page.tsx
  // Handle scenario switching from sidebar
  const handleScenarioChange = useCallback(async (scenarioId: number) => {
    if (scenarioId === activeScenarioId) return;

    // Always hide chart and data readiness immediately on scenario change
    setChartVisible(false);
    setDataReady(false);
    setIsLoading(true);
    setIsTransitioning(true); // Start transition when switching scenarios

    // Save current scenario's input data before switching
    if (activeScenarioId && inputData) {
      setScenarioInputData(prev => ({
        ...prev,
        [activeScenarioId]: inputData
      }));
    }

    // Update active scenario ID. This will trigger the useEffect that loads data.
    setActiveScenarioId(scenarioId);

  }, [activeScenarioId, inputData, setActiveScenarioId, setChartVisible, setDataReady, setIsLoading, setIsTransitioning, setScenarioInputData]); // Added dependencies
```

### 8. Modify `handleOverlayModeToggle` Function

This function explicitly manages the `isTransitioning` state and clears `scenarioData` when turning off overlay mode. Loading of individual scenario data when turning overlay *on* is now handled within this function or by leveraging cached `scenarioInputData`.

```language:app/protected/presentation/page.tsx
```1080:1089:app/protected/presentation/page.tsx
  const handleOverlayModeToggle = useCallback((value: boolean) => {
    setIsOverlayMode(value);
    if (!value) { // Turning off overlay mode
      setIsTransitioning(true); // Start transition
      setChartVisible(false); // Hide chart immediately
      setDataReady(false);
      setScenarioData([]); // Clear overlay data

      // The activeScenarioId is already set to the scenario that was active IN overlay mode.
      // The useEffect watching activeScenarioId will be triggered by setIsTransitioning(true)
      // and handle loading the details for this scenario.

    } else { // Turning on overlay mode
```
```language:app/protected/presentation/page.tsx
```1091:1144:app/protected/presentation/page.tsx
        if (activeScenarioId && allMetrics.length > 0 && inputData) {
          updateScenarioData(); // Add the current scenario to overlay data
        }
        // Load all available scenarios when overlay mode is enabled
        const loadAllScenariosForOverlay = async () => {
           if (availableScenarios.length === 0) return;

           // For each available scenario, load its data if not already in scenarioData
           for (const scenario of availableScenarios) {
             const existingScenario = scenarioData.find(s => s.id === scenario.id);
             if (!existingScenario) {
                // Use cached data if available, otherwise fetch
                if (scenarioInputData[scenario.id]) {
                   const savedData = scenarioInputData[scenario.id];
                   const result = calculateFinancialLife(savedData as unknown as FinancialData);
                   setScenarioData(prev => [
                      ...prev,
                      {
                        id: scenario.id,
                        scenario_name: scenario.scenario_name,
                        household_id: scenario.household_id,
                        household_name: scenario.household_name,
                        allMetrics: result.allMetrics,
                        ending_age: savedData.ending_age
                      }
                   ]);
                } else {
                   // Fetch and process data for the scenario
                   try {
                     const supabase = createClient();
                     const { data: scenarioDetails, error } = await supabase
                       .from('scenarios_data1')
                       .select('*')
                       .eq('id', scenario.id)
                       .single();

                     if (error) throw error;

                     if (scenarioDetails) {
                        const processedData = processScenarioData(scenarioDetails);
                        const result = calculateFinancialLife(processedData as unknown as FinancialData);

                        setScenarioData(prev => [
                           ...prev,
                           {
                             id: scenario.id,
                             scenario_name: scenario.scenario_name,
                             household_id: scenario.household_id,
                             household_name: scenario.household_name,
                             allMetrics: result.allMetrics,
                             ending_age: processedData.ending_age
                           }
                        ]);

                        // Save the input data for future use
                         setScenarioInputData(prev => ({
                           ...prev,
                           [scenario.id]: processedData
                         }));
                     }
                   } catch (error) {
                      console.error(`Error loading scenario ${scenario.id} for overlay:`, error);
                   }
                }
             }
           }
        };
        loadAllScenariosForOverlay();
    }
  }, [activeScenarioId, allMetrics, inputData, updateScenarioData, availableScenarios, scenarioData, scenarioInputData, calculateFinancialLife, processScenarioData]); // Added dependencies
```

### 9. Update Effect to Update `scenarioData` for Overlay Mode

This effect's primary role is now to update the currently active scenario's data within the `scenarioData` array when its metrics change while in overlay mode.

```language:app/protected/presentation/page.tsx
```1148:1152:app/protected/presentation/page.tsx
  // Effect to update scenarioData when overlay mode is toggled or when metrics change
  // This effect is now primarily for updating the active scenario's data in the overlay list
  // when metrics change in overlay mode, or for the initial inclusion when turning overlay on.
  useEffect(() => {
    if (isOverlayMode && activeScenarioId && allMetrics.length > 0 && inputData) {
      updateScenarioData();
    }
    // The loading of ALL scenarios when turning on overlay mode is now handled in handleOverlayModeToggle
  }, [isOverlayMode, activeScenarioId, allMetrics, inputData, updateScenarioData]); // Dependencies
```

### 10. Update Checkbox States Loading Effect

Ensure that the checkbox states are reset to default if no saved data exists for the newly selected scenario.

```language:app/protected/presentation/page.tsx
```1218:1235:app/protected/presentation/page.tsx
      } else {
        // Reset to default if no saved data for the new scenario
         setShowAdditionalData({
            show_savings: false,
            show_investment: false,
            show_individual_investments: false,
            show_kiwisaver: false,
            show_individual_kiwisavers: false,
            show_cashflow: false,
            show_monte_carlo: false,
            show_realistic_netwealth: false,
          });
      }
    }
  }, [activeScenarioId]); // Only depend on activeScenarioId changes
```

### 11. Modify Debounced Recalculation

Simplify the debounced recalculation function to just set `inputData`. The actual calculation is now handled by the dedicated effect that watches `inputData`.

```language:app/protected/presentation/page.tsx
```1250:1255:app/protected/presentation/page.tsx
  // Debounced version of recalculating financial metrics - simplified as calculation is now mainly in useEffect
  const debouncedRecalculate = useCallback(
    debounce((updatedData: InputData) => {
       // When input data is updated via UI controls, set the state
       setInputData(updatedData);
       // The useEffect watching inputData will handle the calculation
    }, 0), // 0ms delay, but debounce still prevents rapid calls
    [setInputData] // setInputData should be stable
  );
```

### 12. Update UI Handlers to Use Debounced Recalculation

Modify the `handleMiscInputChange` and `handleMonteCarloChange` functions to use the `debouncedRecalculate` for relevant parameter changes, and directly set `inputData` for others.

```language:app/protected/presentation/page.tsx
```1257:1276:app/protected/presentation/page.tsx
  const handleMiscInputChange = (name: string, value: number) => {
    setInputData((prevData) => {
      if (prevData === null) return null;

      // Update the appropriate field based on the name
      const updatedData = {
        ...prevData,
        [name]: value,
      };

      // Debounce the recalculation if simulation parameters changed
      if (name === 'inflation_rate' || name === 'num_simulations' || name === 'confidence_interval') {
        debouncedRecalculate(updatedData);
      } else {
        // For other changes, set inputData directly, calculation useEffect will handle it
         return updatedData;
      }
      return updatedData; // Return updatedData for debounced cases too
    });
  };

  const handleMonteCarloChange = (name: string, value: number | boolean) => {
    setInputData((prevData) => {
      if (prevData === null) return null;
```
```language:app/protected/presentation/page.tsx
```1283:1292:app/protected/presentation/page.tsx
      if (name === 'show_monte_carlo' || name === 'num_simulations' || name === 'confidence_interval') {
         debouncedRecalculate(updatedData);
      } else {
         return updatedData;
      }
      return updatedData; // Return updatedData for debounced cases too
    });
  };
```

### 13. Update Initial Scenario Selector Handling

Adjust the initial scenario selection logic to set `activeScenarioId` after processing the data and setting `inputData`.

```language:app/protected/presentation/page.tsx
```1306:1315:app/protected/presentation/page.tsx
            <h1 className="text-2xl font-bold mb-4">Select a Scenario to Visualise</h1>
            <HouseholdSelector onHouseholdSelect={setSelectedHouseholdId} />
            <ScenarioSelector householdId={selectedHouseholdId} onScenarioSelect={(scenarioData) => {
               // When a scenario is selected from the initial selector
               const hasPartner = scenarioData.partner_starting_age !== undefined && scenarioData.partner_starting_age !== null;
               const processedData = processScenarioData({ ...scenarioData, includePartner: hasPartner });
               setInputData(processedData); // This will trigger the calculation useEffect
               setSelectedScenario(scenarioData);
               setHasPartner(hasPartner);
               setActiveScenarioId(scenarioData.id); // Set active ID
            }} />
          </div>
        ) : (
```

### 14. Update Loading State Display

Include `isTransitioning` in the condition for showing the loading spinner.

```language:app/protected/presentation/page.tsx
```1300:1302:app/protected/presentation/page.tsx
      <div className="flex flex-col items-center justify-center flex-grow p-4 overflow-hidden max-h-screen">
        {isLoading || isTransitioning ? ( // Show loading state during transition
          <div className="flex items-center justify-center min-h-screen">
```
```language:app/protected/presentation/page.tsx
```1303:1305:app/protected/presentation/page.tsx
             <div className="flex flex-col items-center space-y-4">
               <Loader2 className="h-8 w-8 animate-spin" />
               <p className="text-sm text-muted-foreground">
                  {isTransitioning ? 'Switching Scenarios...' : 'Loading Scenario...'}
```

### 15. Conditionally Render Charts and Table

Wrap the chart and table components with a check for the `chartVisible` state.

```language:app/protected/presentation/page.tsx
```1335:1337:app/protected/presentation/page.tsx
                        </CardHeader>
                        <CardContent className="flex-grow overflow-hidden transition-all duration-1000 pt-5">
                          <div className={`w-full h-full transition-opacity duration-1000 ${chartVisible ? 'opacity-100' : 'opacity-0'}`}>
                             {/* Render PresentationNetWealth only when chartVisible is true */}
                            {chartVisible && (
                              <ChartContainer
```
```language:app/protected/presentation/page.tsx
```1362:1365:app/protected/presentation/page.tsx
                        </CardContent>
                        <CardFooter className="py-2">
                          <div className="flex w-full items-start gap-2 text-sm">
                           {/* Render KeyMetrics only when chartVisible is true */}
                           {chartVisible && (
                             <KeyMetrics
```
```language:app/protected/presentation/page.tsx
```1404:1406:app/protected/presentation/page.tsx
                         </CardHeader>
                         <CardContent className="flex-grow overflow-hidden transition-all duration-1000">
                           <div className={`w-full h-full transition-opacity duration-1000 ${chartVisible ? 'opacity-100' : 'opacity-0'}`}>
                            {chartVisible && ( // Render charts/table only when chartVisible is true
                              activeTab === 'success' ? (
                                <MonteCarloResults
```
```language:app/protected/presentation/page.tsx
```1485:1490:app/protected/presentation/page.tsx
                                )}
                              </ChartContainer>
                            )}
                             {activeTab === 'table' && allMetrics.length > 0 && chartVisible && ( // Render table only when chartVisible is true
                                <div className="w-full h-full overflow-auto">
                                  <ModellingTableWithTabs
                                    inputData={inputData}
```
```language:app/protected/presentation/page.tsx
```1495:1498:app/protected/presentation/page.tsx
                                 )}
                          </div>
                        </CardContent>
                        {/* ... existing CardFooter ... */}
                         <CardFooter className="py-2">
                          <div className="flex w-full items-start gap-2 text-sm">
```

### 16. Pass `allMetrics` to `ModellingTableWithTabs`

Ensure the `ModellingTableWithTabs` component receives the `allMetrics` data it needs to render correctly.

```language:app/protected/presentation/page.tsx
```1494:1494:app/protected/presentation/page.tsx
                                    inputData={inputData}
                                    mainName={mainName}
                                    partnerName={partnerName}
                                    allMetrics={allMetrics} /* Pass allMetrics to the table */
                                  />
                                </div>
```

These changes implement a more robust state management strategy for scenario switching, ensuring that the UI remains stable and the chart only renders when the necessary data is fully processed, effectively hiding the intermediate rerenders.